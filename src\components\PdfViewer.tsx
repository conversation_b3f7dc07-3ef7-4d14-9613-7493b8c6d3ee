"use client"; // Ensure this component runs only in the browser

import React, { useState, useEffect } from "react";
import {
  <PERSON>er,
  Worker,
  SpecialZoomLevel,
  ScrollMode,
  ViewMode,
} from "@react-pdf-viewer/core";
import { pageNavigationPlugin } from "@react-pdf-viewer/page-navigation";
import { toolbarPlugin } from "@react-pdf-viewer/toolbar";
import * as pdfjs from "pdfjs-dist";
import {
  ArrowLeft,
  ArrowRight,
  Maximize,
  Minimize,
  Play,
  Pause,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

// Import styles
import "@react-pdf-viewer/core/lib/styles/index.css";
import "@react-pdf-viewer/page-navigation/lib/styles/index.css";
import "@react-pdf-viewer/toolbar/lib/styles/index.css";

// Ensure worker is set only in the browser (fixes SSR issue)
if (typeof window !== "undefined") {
  pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
}

// Hook to detect mobile devices
const useIsMobile = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768); // md breakpoint in Tailwind
    };

    checkIsMobile();
    window.addEventListener("resize", checkIsMobile);

    return () => window.removeEventListener("resize", checkIsMobile);
  }, []);

  return isMobile;
};

interface PdfViewerProps {
  fileUrl: string;
}

const PdfViewer: React.FC<PdfViewerProps> = ({ fileUrl }) => {
  const [currentPage, setCurrentPage] = useState(0);
  const [numPages, setNumPages] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [isPresentationMode, setIsPresentationMode] = useState(false);
  const [isAutoPlay, setIsAutoPlay] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const isMobile = useIsMobile();

  // Create page navigation plugin
  const pageNavigationPluginInstance = pageNavigationPlugin();
  const { jumpToPage } = pageNavigationPluginInstance;

  // Create toolbar plugin (hidden when carousel style is enabled on mobile)
  const toolbarPluginInstance = toolbarPlugin();

  const handleDocumentLoad = (e: any) => {
    setNumPages(e.doc.numPages);
    console.log("PDF loaded:", e.doc.numPages, "pages");
  };

  const handlePageChange = (e: any) => {
    setCurrentPage(e.currentPage);
    console.log("Page changed to:", e.currentPage);
  };

  const goToPreviousPage = () => {
    if (currentPage > 0 && !isTransitioning) {
      setIsTransitioning(true);
      jumpToPage(currentPage - 1);
      // Reset transition state after animation
      setTimeout(() => setIsTransitioning(false), 300);
    }
  };

  const goToNextPage = () => {
    if (currentPage < numPages - 1 && !isTransitioning) {
      setIsTransitioning(true);
      jumpToPage(currentPage + 1);
      // Reset transition state after animation
      setTimeout(() => setIsTransitioning(false), 300);
    }
  };

  // Auto-play functionality (works in both normal and presentation mode)
  useEffect(() => {
    if (!isAutoPlay) return;

    const interval = setInterval(() => {
      if (currentPage < numPages - 1) {
        goToNextPage();
      } else {
        setIsAutoPlay(false); // Stop at the end
      }
    }, 3000); // 3 seconds per slide

    return () => clearInterval(interval);
  }, [isAutoPlay, currentPage, numPages]);

  // Auto-hide controls in presentation mode
  useEffect(() => {
    if (!isPresentationMode) return;

    const timer = setTimeout(() => {
      setShowControls(false);
    }, 3000); // Hide controls after 3 seconds

    return () => clearTimeout(timer);
  }, [isPresentationMode, showControls]);

  // Show controls on mouse movement in presentation mode
  useEffect(() => {
    if (!isPresentationMode) return;

    const handleMouseMove = () => {
      setShowControls(true);
    };

    window.addEventListener("mousemove", handleMouseMove);
    return () => window.removeEventListener("mousemove", handleMouseMove);
  }, [isPresentationMode]);

  // Keyboard navigation and fullscreen handling
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "ArrowLeft") {
        event.preventDefault();
        goToPreviousPage();
        if (isPresentationMode) setShowControls(true); // Show controls on navigation
      } else if (event.key === "ArrowRight") {
        event.preventDefault();
        goToNextPage();
        if (isPresentationMode) setShowControls(true); // Show controls on navigation
      } else if (event.key === "Escape" && isPresentationMode) {
        togglePresentationMode();
      } else if (
        event.key === "F11" ||
        (event.key === "f" && !isPresentationMode)
      ) {
        event.preventDefault();
        togglePresentationMode();
      } else if (event.key === "h" && isPresentationMode) {
        // Toggle controls visibility with 'h' key
        event.preventDefault();
        setShowControls(!showControls);
      } else if (event.key === " " && isPresentationMode) {
        // Toggle auto-play with spacebar
        event.preventDefault();
        setIsAutoPlay(!isAutoPlay);
        setShowControls(true);
      } else if (
        event.key === "+" ||
        (event.key === "=" && isPresentationMode)
      ) {
        // Zoom in
        event.preventDefault();
        setZoomLevel(Math.min(3, zoomLevel + 0.25));
        setShowControls(true);
      } else if (event.key === "-" && isPresentationMode) {
        // Zoom out
        event.preventDefault();
        setZoomLevel(Math.max(1, zoomLevel - 0.25));
        setShowControls(true);
      } else if (event.key === "0" && isPresentationMode) {
        // Reset zoom
        event.preventDefault();
        setZoomLevel(1);
        setPanOffset({ x: 0, y: 0 });
        setShowControls(true);
      }
    };

    // Handle fullscreen change events
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!(
        document.fullscreenElement ||
        (document as any).webkitFullscreenElement ||
        (document as any).msFullscreenElement
      );

      // If we exit fullscreen but presentation mode is still active, exit presentation mode
      if (!isCurrentlyFullscreen && isPresentationMode) {
        console.log("Fullscreen exited, exiting presentation mode");
        setIsPresentationMode(false);

        // Unlock orientation when exiting
        if (isMobile && "screen" in window && "orientation" in window.screen) {
          try {
            (window.screen.orientation as any).unlock();
          } catch (error) {
            console.log("Orientation unlock not supported");
          }
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    document.addEventListener("fullscreenchange", handleFullscreenChange);
    document.addEventListener("webkitfullscreenchange", handleFullscreenChange);
    document.addEventListener("msfullscreenchange", handleFullscreenChange);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
      document.removeEventListener(
        "webkitfullscreenchange",
        handleFullscreenChange
      );
      document.removeEventListener(
        "msfullscreenchange",
        handleFullscreenChange
      );
    };
  }, [currentPage, numPages, isPresentationMode, isMobile]);

  // Toggle presentation mode with true fullscreen
  const togglePresentationMode = async () => {
    console.log(
      "Toggling presentation mode. Current state:",
      isPresentationMode
    );
    if (!isPresentationMode) {
      // Entering presentation mode
      console.log("Entering presentation mode");
      setIsPresentationMode(true);
      if (isAutoPlay) setIsAutoPlay(false);

      // Request true fullscreen
      try {
        if (document.documentElement.requestFullscreen) {
          await document.documentElement.requestFullscreen();
        } else if ((document.documentElement as any).webkitRequestFullscreen) {
          await (document.documentElement as any).webkitRequestFullscreen();
        } else if ((document.documentElement as any).msRequestFullscreen) {
          await (document.documentElement as any).msRequestFullscreen();
        }
        console.log("Fullscreen mode activated");
      } catch (error) {
        console.log("Fullscreen not supported or denied:", error);
      }

      // Request landscape orientation on mobile devices
      if (isMobile && "screen" in window && "orientation" in window.screen) {
        try {
          (window.screen.orientation as any).lock("landscape").catch(() => {
            // Fallback: just enter presentation mode without orientation lock
            console.log("Landscape orientation lock not supported");
          });
        } catch (error) {
          console.log("Orientation API not supported");
        }
      }
    } else {
      // Exiting presentation mode
      console.log("Exiting presentation mode");
      setIsPresentationMode(false);

      // Exit fullscreen
      try {
        if (document.exitFullscreen) {
          await document.exitFullscreen();
        } else if ((document as any).webkitExitFullscreen) {
          await (document as any).webkitExitFullscreen();
        } else if ((document as any).msExitFullscreen) {
          await (document as any).msExitFullscreen();
        }
        console.log("Fullscreen mode exited");
      } catch (error) {
        console.log("Error exiting fullscreen:", error);
      }

      // Unlock orientation when exiting
      if (isMobile && "screen" in window && "orientation" in window.screen) {
        try {
          (window.screen.orientation as any).unlock();
        } catch (error) {
          console.log("Orientation unlock not supported");
        }
      }
    }
  };

  // Toggle auto-play
  const toggleAutoPlay = () => {
    setIsAutoPlay(!isAutoPlay);
  };

  // Presentation mode - completely separate rendering
  if (isPresentationMode) {
    return (
      <div
        className="fixed inset-0 z-50 bg-black flex items-center justify-center fullscreen-presentation"
        style={{
          overflow: "hidden", // Prevent content from escaping the container
        }}
        onTouchStart={(e) => {
          const touch = e.touches[0];
          e.currentTarget.dataset.startX = touch.clientX.toString();
          e.currentTarget.dataset.startY = touch.clientY.toString();
          e.currentTarget.dataset.isValidTouch = "true";

          // Handle pinch-to-zoom
          if (e.touches.length === 2) {
            const touch1 = e.touches[0];
            const touch2 = e.touches[1];
            const distance = Math.sqrt(
              Math.pow(touch2.clientX - touch1.clientX, 2) +
                Math.pow(touch2.clientY - touch1.clientY, 2)
            );
            e.currentTarget.dataset.initialPinchDistance = distance.toString();
            e.currentTarget.dataset.initialZoom = zoomLevel.toString();
            e.currentTarget.dataset.isPinching = "true";
            setIsDragging(false);
          } else {
            e.currentTarget.dataset.isPinching = "false";
            // If zoomed in, prepare for dragging instead of swiping
            if (zoomLevel > 1) {
              e.currentTarget.dataset.initialPanX = panOffset.x.toString();
              e.currentTarget.dataset.initialPanY = panOffset.y.toString();
              setIsDragging(true);
            }
          }

          // Show controls on touch
          setShowControls(true);
        }}
        onTouchMove={(e) => {
          if (e.currentTarget.dataset.isValidTouch !== "true") {
            return;
          }

          // Handle pinch-to-zoom
          if (
            e.touches.length === 2 &&
            e.currentTarget.dataset.isPinching === "true"
          ) {
            e.preventDefault();
            e.stopPropagation();

            const touch1 = e.touches[0];
            const touch2 = e.touches[1];
            const currentDistance = Math.sqrt(
              Math.pow(touch2.clientX - touch1.clientX, 2) +
                Math.pow(touch2.clientY - touch1.clientY, 2)
            );

            const initialDistance = parseFloat(
              e.currentTarget.dataset.initialPinchDistance || "0"
            );
            const initialZoom = parseFloat(
              e.currentTarget.dataset.initialZoom || "1"
            );

            if (initialDistance > 0) {
              const scale = currentDistance / initialDistance;
              const newZoom = Math.max(1, Math.min(3, initialZoom * scale));
              setZoomLevel(newZoom);

              // Reset pan if zooming back to 1x
              if (newZoom <= 1) {
                setPanOffset({ x: 0, y: 0 });
              }
            }
            return;
          }

          // Handle single touch (drag when zoomed, swipe when normal)
          if (e.touches.length === 1) {
            const startX = parseFloat(e.currentTarget.dataset.startX || "0");
            const startY = parseFloat(e.currentTarget.dataset.startY || "0");
            const currentX = e.touches[0].clientX;
            const currentY = e.touches[0].clientY;

            if (zoomLevel > 1) {
              // When zoomed in, handle dragging/panning
              e.preventDefault();
              e.stopPropagation();

              const initialPanX = parseFloat(
                e.currentTarget.dataset.initialPanX || "0"
              );
              const initialPanY = parseFloat(
                e.currentTarget.dataset.initialPanY || "0"
              );

              const deltaX = currentX - startX;
              const deltaY = currentY - startY;

              // Calculate constraints based on zoom level and actual content
              const containerWidth = window.innerWidth;
              const containerHeight = window.innerHeight;

              // Calculate the maximum pan distance based on how much the content extends beyond the viewport
              // When zoomed, content can extend beyond viewport by (zoomLevel - 1) * viewport / 2 on each side
              const maxPanX = Math.max(
                0,
                (containerWidth * (zoomLevel - 1)) / 2
              );
              const maxPanY = Math.max(
                0,
                (containerHeight * (zoomLevel - 1)) / 2
              );

              // Constrain pan offset to keep content visible
              const newPanX = Math.max(
                -maxPanX,
                Math.min(maxPanX, initialPanX + deltaX)
              );
              const newPanY = Math.max(
                -maxPanY,
                Math.min(maxPanY, initialPanY + deltaY)
              );

              setPanOffset({
                x: newPanX,
                y: newPanY,
              });
            } else {
              // When at normal zoom, handle swipe gestures
              const diffX = Math.abs(startX - currentX);
              const diffY = Math.abs(startY - currentY);

              // Prevent default for both horizontal and vertical swipes in presentation mode
              if (
                (diffX > 20 && diffX > diffY) ||
                (diffY > 20 && diffY > diffX)
              ) {
                e.preventDefault();
                e.stopPropagation();
              }
            }
          }
        }}
        onTouchEnd={(e) => {
          if (e.currentTarget.dataset.isValidTouch !== "true") {
            return;
          }

          e.stopPropagation();

          // Reset pinch state
          e.currentTarget.dataset.isPinching = "false";
          e.currentTarget.dataset.initialPinchDistance = "0";
          e.currentTarget.dataset.initialZoom = "1";

          // Reset dragging state
          setIsDragging(false);

          // Only handle swipe navigation if it wasn't a pinch gesture and not zoomed in
          if (e.changedTouches.length === 1 && zoomLevel <= 1) {
            const startX = parseFloat(e.currentTarget.dataset.startX || "0");
            const startY = parseFloat(e.currentTarget.dataset.startY || "0");
            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;

            const diffX = startX - endX;
            const diffY = startY - endY;

            // Reset the valid touch flag
            e.currentTarget.dataset.isValidTouch = "false";

            // Check for horizontal swipes (left/right navigation)
            if (
              Math.abs(diffX) > Math.abs(diffY) &&
              Math.abs(diffX) > 100 &&
              !isTransitioning
            ) {
              if (diffX > 0 && currentPage < numPages - 1) {
                // Swiped left - go to next page
                goToNextPage();
              } else if (diffX < 0 && currentPage > 0) {
                // Swiped right - go to previous page
                goToPreviousPage();
              }
            }
            // Check for vertical swipes (up/down navigation)
            else if (
              Math.abs(diffY) > Math.abs(diffX) &&
              Math.abs(diffY) > 100 &&
              !isTransitioning
            ) {
              if (diffY > 0 && currentPage < numPages - 1) {
                // Swiped up - go to next page
                goToNextPage();
              } else if (diffY < 0 && currentPage > 0) {
                // Swiped down - go to previous page
                goToPreviousPage();
              }
            }
          } else {
            // Reset the valid touch flag for multi-touch or zoomed state
            e.currentTarget.dataset.isValidTouch = "false";
          }
        }}
      >
        <div
          style={{
            width: "100vw",
            height: "100vh",
            overflow: "hidden",
            position: "relative",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Worker
            workerUrl={`https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`}
          >
            <div
              style={{
                width: "100%",
                height: "100%",
                transform: `scale(${zoomLevel}) translate(${panOffset.x}px, ${panOffset.y}px)`,
                transformOrigin: "center center",
                transition: "transform 0.1s ease-out",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "transparent",
                position: "relative",
              }}
            >
              <Viewer
                fileUrl={fileUrl}
                plugins={[pageNavigationPluginInstance]}
                onDocumentLoad={handleDocumentLoad}
                onPageChange={handlePageChange}
                defaultScale={SpecialZoomLevel.PageFit}
                scrollMode={ScrollMode.Page}
                viewMode={ViewMode.SinglePage}
              />
            </div>
          </Worker>
        </div>

        {/* Presentation Controls */}
        <div
          className={cn(
            "absolute top-4 right-4 flex flex-col gap-2 z-20 transition-opacity duration-300",
            showControls ? "opacity-100" : "opacity-0 pointer-events-none"
          )}
        >
          <Button
            variant="outline"
            size="icon"
            className="h-10 w-10 rounded-full bg-white/90 hover:bg-white shadow-md"
            onClick={togglePresentationMode}
            title="Exit Presentation Mode (ESC)"
          >
            <Minimize className="h-5 w-5" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className={cn(
              "h-10 w-10 rounded-full bg-white/90 hover:bg-white shadow-md",
              isAutoPlay && "bg-primary text-white"
            )}
            onClick={() => {
              setIsAutoPlay(!isAutoPlay);
              setShowControls(true);
            }}
            title={
              isAutoPlay ? "Stop Auto-play (Space)" : "Start Auto-play (Space)"
            }
          >
            {isAutoPlay ? (
              <Pause className="h-5 w-5" />
            ) : (
              <Play className="h-5 w-5" />
            )}
          </Button>

          {/* Zoom Controls */}
          <div className="flex flex-col gap-1">
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 rounded-full bg-white/90 hover:bg-white shadow-md text-xs"
              onClick={() => {
                setZoomLevel(Math.min(3, zoomLevel + 0.25));
                setShowControls(true);
              }}
              title="Zoom In"
            >
              +
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 rounded-full bg-white/90 hover:bg-white shadow-md text-xs"
              onClick={() => {
                const newZoom = Math.max(1, zoomLevel - 0.25);
                setZoomLevel(newZoom);
                if (newZoom <= 1) {
                  setPanOffset({ x: 0, y: 0 });
                }
                setShowControls(true);
              }}
              title="Zoom Out"
              disabled={zoomLevel <= 1}
            >
              -
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 rounded-full bg-white/90 hover:bg-white shadow-md text-xs"
              onClick={() => {
                setZoomLevel(1);
                setPanOffset({ x: 0, y: 0 });
                setShowControls(true);
              }}
              title="Reset Zoom"
            >
              1x
            </Button>
          </div>
        </div>

        {/* Navigation Buttons */}

        {/* Page Counter */}
        {numPages > 0 && (
          <div
            className={cn(
              "absolute bottom-4 left-1/2 -translate-x-1/2 px-3 py-1 rounded-full text-sm font-medium z-20",
              isPresentationMode ? "" : "",
              showControls ? "opacity-100" : "opacity-0 pointer-events-none"
            )}
          >
            <div className="flex items-center gap-3 bg-white rounded-full px-4 py-2 shadow-md">
              <Button
                variant="ghost"
                size="sm"
                onClick={goToPreviousPage}
                disabled={currentPage === 0}
                className="h-8 w-8 p-0"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm font-medium min-w-[60px] text-center">
                {currentPage + 1} / {numPages}
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={goToNextPage}
                disabled={currentPage === numPages - 1}
                className="h-8 w-8 p-0"
              >
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Debug Info */}
        <div
          className={cn(
            "absolute top-4 left-4 bg-green-500 text-white px-4 py-2 rounded text-sm z-50 font-mono transition-opacity duration-300",
            !showControls && "opacity-0"
          )}
        >
          🖥️ ZOOM: {Math.round(zoomLevel * 100)}% | PAN: X:
          {Math.round(panOffset.x)} Y:{Math.round(panOffset.y)} | Page:{" "}
          {currentPage + 1}/{numPages}
        </div>
        {/* <div className="absolute top-4 left-4 bg-green-500 text-white px-4 py-2 rounded text-sm z-50 font-mono">
          �️ FULLSCREEN + 4-WAY SWIPE | Pages: {numPages} | Current: {currentPage + 1}
        </div> */}
      </div>
    );
  }

  // Mobile carousel-style view
  if (isMobile) {
    return (
      <div
        className={cn(
          "relative w-full",
          isPresentationMode
            ? "fixed inset-0 z-50 bg-black pdf-presentation-mode"
            : "min-h-screen bg-gray-50"
        )}
      >
        {/* Mobile Carousel-style PDF Viewer */}
        <div className="relative">
          <Worker
            workerUrl={`https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`}
          >
            <div
              className={cn(
                "pdf-viewer-container",
                isTransitioning && "opacity-90"
              )}
              style={{
                height: isPresentationMode ? "100vh" : "calc(100vh - 120px)",
                width: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
              onTouchStart={(e) => {
                const touch = e.touches[0];
                const rect = e.currentTarget.getBoundingClientRect();

                // Only handle if touch is within the PDF container bounds
                if (
                  touch.clientX >= rect.left &&
                  touch.clientX <= rect.right &&
                  touch.clientY >= rect.top &&
                  touch.clientY <= rect.bottom
                ) {
                  e.currentTarget.dataset.startX = touch.clientX.toString();
                  e.currentTarget.dataset.startY = touch.clientY.toString();
                  e.currentTarget.dataset.isValidTouch = "true";
                } else {
                  e.currentTarget.dataset.isValidTouch = "false";
                }
              }}
              onTouchMove={(e) => {
                // Only handle if this was a valid touch start within our container
                if (e.currentTarget.dataset.isValidTouch !== "true") {
                  return;
                }

                const startX = parseFloat(
                  e.currentTarget.dataset.startX || "0"
                );
                const startY = parseFloat(
                  e.currentTarget.dataset.startY || "0"
                );
                const currentX = e.touches[0].clientX;
                const currentY = e.touches[0].clientY;

                const diffX = Math.abs(startX - currentX);
                const diffY = Math.abs(startY - currentY);

                // Only prevent default if it's clearly a horizontal swipe
                if (diffX > diffY && diffX > 20) {
                  e.preventDefault();
                  e.stopPropagation();
                }
              }}
              onTouchEnd={(e) => {
                // Only handle if this was a valid touch start within our container
                if (e.currentTarget.dataset.isValidTouch !== "true") {
                  return;
                }

                e.stopPropagation();

                const startX = parseFloat(
                  e.currentTarget.dataset.startX || "0"
                );
                const startY = parseFloat(
                  e.currentTarget.dataset.startY || "0"
                );
                const endX = e.changedTouches[0].clientX;
                const endY = e.changedTouches[0].clientY;

                const diffX = startX - endX;
                const diffY = startY - endY;

                // Reset the valid touch flag
                e.currentTarget.dataset.isValidTouch = "false";

                // Only trigger if horizontal swipe is more significant than vertical
                // and swipe distance is sufficient
                if (
                  Math.abs(diffX) > Math.abs(diffY) &&
                  Math.abs(diffX) > 120 &&
                  !isTransitioning
                ) {
                  if (diffX > 0 && currentPage < numPages - 1) {
                    // Swiped left - go to next page
                    goToNextPage();
                  } else if (diffX < 0 && currentPage > 0) {
                    // Swiped right - go to previous page
                    goToPreviousPage();
                  }
                }
              }}
            >
              <Viewer
                fileUrl={fileUrl}
                plugins={[pageNavigationPluginInstance]}
                onDocumentLoad={handleDocumentLoad}
                onPageChange={handlePageChange}
                defaultScale={
                  isPresentationMode ? 1.5 : SpecialZoomLevel.PageWidth
                }
                scrollMode={ScrollMode.Page}
                viewMode={ViewMode.SinglePage}
              />
            </div>
          </Worker>

          {/* Page Indicator */}
          {numPages > 0 && (
            <div
              className={cn(
                "absolute bottom-4 left-1/2 -translate-x-1/2 px-3 py-1 rounded-full text-sm font-medium z-20",
                isPresentationMode ? "" : ""
              )}
            >
              <div className="flex items-center gap-3 bg-white rounded-full px-4 py-2 shadow-md">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={goToPreviousPage}
                  disabled={currentPage === 0}
                  className="h-8 w-8 p-0"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm font-medium min-w-[60px] text-center">
                  {currentPage + 1} / {numPages}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={currentPage === numPages - 1}
                  className="h-8 w-8 p-0"
                >
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}

          {/* Debug Info in Presentation Mode */}
          {/* {isPresentationMode && (
            <div className="absolute top-4 left-4 bg-green-500 text-white px-4 py-2 rounded text-sm z-50 font-mono">
              📺 PRESENTATION | Pages: {numPages} | Current: {currentPage + 1} |
              Scale: 1.5x
            </div>
          )} */}

          {/* Landscape Mode Indicator for Mobile */}
          {isPresentationMode && isMobile && (
            <div className="landscape-indicator absolute top-12 left-4 bg-white/20 text-white px-3 py-1 rounded-full text-xs backdrop-blur-sm z-20">
              📱 Rotate to landscape for best experience
            </div>
          )}

          {/* Presentation Mode Controls */}
          <div className="absolute top-4 right-4 flex flex-col gap-2 z-20">
            {isPresentationMode ? (
              <>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-10 w-10 rounded-full bg-white/90 hover:bg-white shadow-md"
                  onClick={togglePresentationMode}
                  title="Exit Presentation Mode (ESC)"
                >
                  <Minimize className="h-5 w-5" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  className={cn(
                    "h-10 w-10 rounded-full bg-white/90 hover:bg-white shadow-md",
                    isAutoPlay && "bg-primary text-white"
                  )}
                  onClick={toggleAutoPlay}
                  title={isAutoPlay ? "Stop Auto-play" : "Start Auto-play"}
                >
                  {isAutoPlay ? (
                    <Pause className="h-5 w-5" />
                  ) : (
                    <Play className="h-5 w-5" />
                  )}
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-10 w-10 rounded-full bg-white/90 hover:bg-white shadow-md"
                  onClick={togglePresentationMode}
                  title="Enter Presentation Mode (F)"
                >
                  <Maximize className="h-5 w-5" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  className={cn(
                    "h-10 w-10 rounded-full bg-white/90 hover:bg-white shadow-md",
                    isAutoPlay && "bg-primary text-white"
                  )}
                  onClick={toggleAutoPlay}
                  title={isAutoPlay ? "Stop Auto-play" : "Start Auto-play"}
                >
                  {isAutoPlay ? (
                    <Pause className="h-5 w-5" />
                  ) : (
                    <Play className="h-5 w-5" />
                  )}
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Default PDF viewer (desktop behavior with toolbar)
  return (
    <div className="w-full min-h-screen">
      <Worker
        workerUrl={`https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`}
      >
        <Viewer
          fileUrl={fileUrl}
          plugins={[toolbarPluginInstance, pageNavigationPluginInstance]}
        />
      </Worker>
    </div>
  );
};

export default PdfViewer;
